# Professional Scratch Algorithms from xwax

Этот документ описывает профессиональные алгоритмы, интегрированные из xwax DVS (Digital Vinyl System) в наш Virtual Vinyl Turntable.

## Основные улучшения

### 1. Профессиональный Pitch Filter (Фильтр скорости)

**Источник**: `pitch.h` из xwax  
**Алгоритм**: Kalman-подобный фильтр для сглаживания определения скорости

```javascript
class PitchFilter {
    constructor() {
        this.ALPHA = 1.0 / 512;    // Фильтр позиции
        this.BETA = this.ALPHA / 256; // Фильтр скорости
        this.x = 0.0; // позиция
        this.v = 0.0; // скорость (pitch)
    }
    
    addObservation(dx, dt) {
        const predicted_x = this.x + this.v * dt;
        const residual_x = dx - predicted_x;
        
        this.x = predicted_x + residual_x * this.ALPHA;
        this.v = predicted_v + residual_x * this.BETA / dt;
    }
}
```

**Преимущества**:
- Сглаживает резкие изменения скорости
- Предсказывает движение на основе истории
- Устраняет джиттер и шум

### 2. Кубическая интерполяция аудио

**Источник**: `player.c` из xwax, функция `cubic_interpolate`  
**Алгоритм**: Высококачественная интерполяция для ресэмплинга

```javascript
static cubicInterpolate(y, mu) {
    const mu2 = mu * mu;
    const a0 = y[3] - y[2] - y[0] + y[1];
    const a1 = y[0] - y[1] - a0;
    const a2 = y[2] - y[0];
    const a3 = y[1];
    
    return (mu * mu2 * a0) + (mu2 * a1) + (mu * a2) + a3;
}
```

**Преимущества**:
- Минимизирует алиасинг при изменении скорости
- Лучшее качество звука по сравнению с линейной интерполяцией
- Профессиональный стандарт в DJ софте

### 3. Система синхронизации

**Источник**: `player.c` из xwax, функция `retarget`  
**Алгоритм**: Автоматическая синхронизация позиции воспроизведения

```javascript
retarget() {
    const diff = this.position - this.targetPosition;
    
    if (Math.abs(diff) > SKIP_THRESHOLD) {
        // Прыжок к новой позиции
        this.position = this.targetPosition;
    } else if (Math.abs(this.pitch) > SYNC_PITCH) {
        // Плавная коррекция скорости
        this.syncPitch = this.pitch / (diff / SYNC_TIME + this.pitch);
    }
}
```

**Константы из xwax**:
- `SYNC_TIME = 0.5` - время достижения синхронизации
- `SKIP_THRESHOLD = 0.125` - порог для прыжка позиции
- `SYNC_PITCH = 0.05` - минимальная скорость для синхронизации

### 4. Dithering (Дизеринг)

**Источник**: `player.c` из xwax, функция `dither`  
**Алгоритм**: Псевдослучайный шум для улучшения качества звука

```javascript
static dither() {
    // Linear Feedback Shift Register для генерации шума
    const bit = (x ^ (x >> 1) ^ (x >> 21) ^ (x >> 31)) & 1;
    x = x << 1 | bit;
    return (x & 0xfff) / 4096 - 0.5;
}
```

**Преимущества**:
- Уменьшает квантизационные искажения
- Улучшает восприятие динамического диапазона
- Стандарт в профессиональном аудио

## Технические детали

### Обработка скретча в реальном времени

1. **Захват движения винила** → `VinylUI.updateGrab()`
2. **Фильтрация pitch** → `PitchFilter.addObservation()`
3. **Установка target position** → `AudioEngine.updateScratchPosition()`
4. **Синхронизация** → `AudioEngine.retarget()`
5. **Воспроизведение** → `AudioEngine.playScratchSnippetPro()`

### Качество аудио

- **Частота дискретизации**: 44.1 kHz
- **Интерполяция**: Кубическая (4 точки)
- **Дизеринг**: LFSR псевдослучайный
- **Буферизация**: 512 сэмплов для низкой задержки

### Производительность

- **Фильтрация**: O(1) операции
- **Интерполяция**: 4 сэмпла на выход
- **Обновления**: 100ms интервал для UI
- **Аудио**: Реальное время без пропусков

## Сравнение с оригинальной реализацией

| Аспект | Оригинал | С xwax алгоритмами |
|--------|----------|-------------------|
| Pitch detection | Простое усреднение | Kalman-подобный фильтр |
| Интерполяция | Линейная | Кубическая |
| Синхронизация | Отсутствует | Профессиональная |
| Качество звука | Базовое | Студийное |
| Стабильность | Джиттер | Сглаженное |

## Настройки для разных стилей

### Для точного скретча (Hip-Hop)
```javascript
pitchFilter.ALPHA = 1.0 / 256;  // Более быстрый отклик
SYNC_TIME = 0.25;               // Быстрая синхронизация
```

### Для плавного микширования (House/Techno)
```javascript
pitchFilter.ALPHA = 1.0 / 1024; // Более плавный отклик
SYNC_TIME = 1.0;                // Медленная синхронизация
```

### Для экстремального скретча (Turntablism)
```javascript
SKIP_THRESHOLD = 0.05;          // Меньший порог прыжка
SYNC_PITCH = 0.01;              // Синхронизация на низких скоростях
```

## Источники

- **xwax**: http://xwax.org/ - Open-source DVS для Linux
- **GitHub**: https://github.com/xwax/xwax
- **Автор**: Mark Hills <<EMAIL>>
- **Лицензия**: GPL v3

## Благодарности

Огромная благодарность Mark Hills и сообществу xwax за создание профессиональных алгоритмов DVS с открытым исходным кодом, которые сделали возможным создание высококачественного веб-турнтейбла.
