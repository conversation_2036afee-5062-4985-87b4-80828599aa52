<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Turntable Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .debug-panel { background: #2a2a2a; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .status.ok { background: #4ecdc4; }
        .status.error { background: #ff6b6b; }
        .status.warning { background: #ffd93d; color: black; }
        button { padding: 10px 20px; margin: 5px; background: #4ecdc4; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45b7aa; }
        #log { background: #000; padding: 10px; border-radius: 4px; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Virtual Vinyl Turntable - Debug Panel</h1>
    
    <div class="debug-panel">
        <h2>System Status</h2>
        <div id="audioContextStatus" class="status">Checking Audio Context...</div>
        <div id="webAudioStatus" class="status">Checking Web Audio API...</div>
        <div id="fileApiStatus" class="status">Checking File API...</div>
    </div>
    
    <div class="debug-panel">
        <h2>Quick Tests</h2>
        <button onclick="testAudioContext()">Test Audio Context</button>
        <button onclick="testOscillator()">Test Oscillator</button>
        <button onclick="testFileAPI()">Test File API</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="debug-panel">
        <h2>Audio Test</h2>
        <button onclick="playTestTone()">Play Test Tone</button>
        <button onclick="stopTestTone()">Stop Test Tone</button>
        <input type="range" id="frequency" min="200" max="800" value="440" oninput="updateFrequency()">
        <span id="freqDisplay">440 Hz</span>
    </div>
    
    <div class="debug-panel">
        <h2>Debug Log</h2>
        <div id="log"></div>
    </div>
    
    <div class="debug-panel">
        <h2>Go to Main App</h2>
        <a href="/" style="color: #4ecdc4;">← Back to Virtual Turntable</a>
    </div>

    <script>
        let audioContext;
        let testOscillator;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warning' ? '#ffd93d' : '#4ecdc4';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function updateStatus(elementId, message, status) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${status}`;
        }
        
        function checkSystemStatus() {
            // Check Audio Context
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                updateStatus('audioContextStatus', `Audio Context: ${audioContext.state} (${audioContext.sampleRate}Hz)`, 'ok');
                log(`Audio Context initialized: ${audioContext.state}`);
            } catch (error) {
                updateStatus('audioContextStatus', 'Audio Context: Not supported', 'error');
                log(`Audio Context error: ${error.message}`, 'error');
            }
            
            // Check Web Audio API
            if (window.AudioContext || window.webkitAudioContext) {
                updateStatus('webAudioStatus', 'Web Audio API: Supported', 'ok');
                log('Web Audio API is supported');
            } else {
                updateStatus('webAudioStatus', 'Web Audio API: Not supported', 'error');
                log('Web Audio API is not supported', 'error');
            }
            
            // Check File API
            if (window.File && window.FileReader && window.FileList && window.Blob) {
                updateStatus('fileApiStatus', 'File API: Supported', 'ok');
                log('File API is supported');
            } else {
                updateStatus('fileApiStatus', 'File API: Not supported', 'error');
                log('File API is not supported', 'error');
            }
        }
        
        async function testAudioContext() {
            try {
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }
                
                if (audioContext.state === 'suspended') {
                    await audioContext.resume();
                    log('Audio context resumed');
                }
                
                log(`Audio context state: ${audioContext.state}`);
                log(`Sample rate: ${audioContext.sampleRate}Hz`);
                log(`Current time: ${audioContext.currentTime.toFixed(3)}s`);
                
            } catch (error) {
                log(`Audio context test failed: ${error.message}`, 'error');
            }
        }
        
        function testOscillator() {
            try {
                if (!audioContext) {
                    throw new Error('Audio context not initialized');
                }
                
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.5);
                
                log('Oscillator test: 440Hz sine wave for 0.5s');
                
            } catch (error) {
                log(`Oscillator test failed: ${error.message}`, 'error');
            }
        }
        
        function testFileAPI() {
            try {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'audio/*';
                
                input.onchange = function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        log(`File selected: ${file.name}`);
                        log(`File type: ${file.type}`);
                        log(`File size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
                        
                        const reader = new FileReader();
                        reader.onload = function() {
                            log('File read successfully');
                            log(`Data length: ${reader.result.byteLength} bytes`);
                        };
                        reader.onerror = function() {
                            log('File read failed', 'error');
                        };
                        reader.readAsArrayBuffer(file);
                    }
                };
                
                input.click();
                log('File API test: Select an audio file');
                
            } catch (error) {
                log(`File API test failed: ${error.message}`, 'error');
            }
        }
        
        function playTestTone() {
            try {
                if (testOscillator) {
                    stopTestTone();
                }
                
                if (!audioContext) {
                    throw new Error('Audio context not initialized');
                }
                
                const frequency = document.getElementById('frequency').value;
                
                testOscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                testOscillator.type = 'sine';
                testOscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                
                testOscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                testOscillator.start();
                
                log(`Playing test tone: ${frequency}Hz`);
                
            } catch (error) {
                log(`Test tone failed: ${error.message}`, 'error');
            }
        }
        
        function stopTestTone() {
            if (testOscillator) {
                testOscillator.stop();
                testOscillator = null;
                log('Test tone stopped');
            }
        }
        
        function updateFrequency() {
            const frequency = document.getElementById('frequency').value;
            document.getElementById('freqDisplay').textContent = frequency + ' Hz';
            
            if (testOscillator) {
                testOscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            }
        }
        
        // Initialize on load
        window.onload = function() {
            checkSystemStatus();
            log('Debug panel initialized');
        };
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            log(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log(args.join(' '), 'warning');
        };
    </script>
</body>
</html>
