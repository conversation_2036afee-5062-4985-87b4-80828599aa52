<!DOCTYPE html>
<html>
<head>
    <title>Create Test MP3</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #4ecdc4; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #45b7aa; }
    </style>
</head>
<body>
    <h1>Create Test MP3 for Turntable</h1>
    <p>This will create a simple test audio file with a beat pattern.</p>
    
    <button onclick="createTestAudio()">Create Test Audio (WAV)</button>
    <button onclick="createDrumBeat()">Create Drum Beat (WAV)</button>
    
    <div id="status"></div>
    
    <script>
        function createTestAudio() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const sampleRate = audioContext.sampleRate;
            const duration = 30; // 30 seconds
            const numSamples = sampleRate * duration;
            
            const buffer = audioContext.createBuffer(2, numSamples, sampleRate);
            
            for (let channel = 0; channel < 2; channel++) {
                const channelData = buffer.getChannelData(channel);
                
                for (let i = 0; i < numSamples; i++) {
                    const t = i / sampleRate;
                    
                    // Create a simple melody with bass
                    const bass = Math.sin(2 * Math.PI * 60 * t) * 0.3;
                    const melody = Math.sin(2 * Math.PI * 440 * t) * 0.2 * Math.sin(2 * Math.PI * 0.5 * t);
                    const beat = Math.sin(2 * Math.PI * 120 * t) * 0.4 * (Math.floor(t * 2) % 2);
                    
                    channelData[i] = bass + melody + beat;
                }
            }
            
            downloadBuffer(buffer, 'test-audio-30s.wav');
            document.getElementById('status').innerHTML = 'Test audio created! Download should start automatically.';
        }
        
        function createDrumBeat() {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const sampleRate = audioContext.sampleRate;
            const duration = 20; // 20 seconds
            const numSamples = sampleRate * duration;
            
            const buffer = audioContext.createBuffer(2, numSamples, sampleRate);
            
            for (let channel = 0; channel < 2; channel++) {
                const channelData = buffer.getChannelData(channel);
                
                for (let i = 0; i < numSamples; i++) {
                    const t = i / sampleRate;
                    const beatTime = t % 1; // 1 second beat cycle
                    
                    let sample = 0;
                    
                    // Kick drum on beats 0 and 0.5
                    if (beatTime < 0.1 || (beatTime > 0.5 && beatTime < 0.6)) {
                        sample += Math.sin(2 * Math.PI * 60 * t) * Math.exp(-beatTime * 10) * 0.8;
                    }
                    
                    // Hi-hat on off-beats
                    if (beatTime > 0.25 && beatTime < 0.3) {
                        sample += (Math.random() * 2 - 1) * 0.1 * Math.exp(-(beatTime - 0.25) * 20);
                    }
                    if (beatTime > 0.75 && beatTime < 0.8) {
                        sample += (Math.random() * 2 - 1) * 0.1 * Math.exp(-(beatTime - 0.75) * 20);
                    }
                    
                    // Bass line
                    sample += Math.sin(2 * Math.PI * 80 * t) * 0.2;
                    
                    channelData[i] = sample;
                }
            }
            
            downloadBuffer(buffer, 'drum-beat-20s.wav');
            document.getElementById('status').innerHTML = 'Drum beat created! Download should start automatically.';
        }
        
        function downloadBuffer(buffer, filename) {
            const wavData = bufferToWav(buffer);
            const blob = new Blob([wavData], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function bufferToWav(buffer) {
            const length = buffer.length;
            const numberOfChannels = buffer.numberOfChannels;
            const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
            const view = new DataView(arrayBuffer);
            
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            // WAV header
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * numberOfChannels * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * numberOfChannels * 2, true);
            
            // Convert samples
            let offset = 44;
            for (let i = 0; i < length; i++) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
                    view.setInt16(offset, sample * 0x7FFF, true);
                    offset += 2;
                }
            }
            
            return arrayBuffer;
        }
    </script>
</body>
</html>
