<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Vinyl Turntable</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            font-family: 'Arial', sans-serif;
        }
        
        .vinyl-container {
            perspective: 1000px;
        }
        
        .vinyl-record {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle at center, #1a1a1a 20%, #333 40%, #1a1a1a 60%, #333 80%);
            position: relative;
            cursor: grab;
            transition: transform 0.1s ease-out;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .vinyl-record:active {
            cursor: grabbing;
        }
        
        .vinyl-label {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }
        
        .vinyl-grooves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: repeating-radial-gradient(
                circle at center,
                transparent 0px,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 3px
            );
        }
        
        .control-panel {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .waveform-container {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            height: 80px;
            position: relative;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
            transition: width 0.1s ease;
        }
        
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover,
        .upload-area.dragover {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff6b6b;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.playing {
            background: #4ecdc4;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .spinning {
            animation: spin linear infinite;
        }

        @keyframes scratch-jitter {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-1px) translateY(1px); }
            50% { transform: translateX(1px) translateY(-1px); }
            75% { transform: translateX(-1px) translateY(-1px); }
        }

        .scratching {
            animation: scratch-jitter 0.1s infinite;
        }

        @keyframes glow-pulse {
            0%, 100% { box-shadow: 0 0 20px rgba(78, 205, 196, 0.3), 0 10px 30px rgba(0, 0, 0, 0.5); }
            50% { box-shadow: 0 0 40px rgba(78, 205, 196, 0.8), 0 10px 30px rgba(0, 0, 0, 0.5); }
        }

        .glow-pulse {
            animation: glow-pulse 1s ease-in-out infinite;
        }

        .vinyl-record.loading {
            animation: spin 2s linear infinite;
            opacity: 0.7;
        }

        .control-panel {
            transition: all 0.3s ease;
        }

        .control-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .waveform-container canvas {
            transition: filter 0.3s ease;
        }

        .waveform-container.active canvas {
            filter: brightness(1.2) saturate(1.2);
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .vinyl-record {
                width: 250px;
                height: 250px;
            }

            .vinyl-label {
                width: 80px;
                height: 80px;
                font-size: 10px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 4rem;
            }

            .control-panel {
                margin: 0 auto;
                max-width: 400px;
            }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .vinyl-record {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
    </style>
</head>
<body class="min-h-screen text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold mb-2 bg-gradient-to-r from-pink-400 to-cyan-400 bg-clip-text text-transparent">
                Virtual Vinyl Turntable
            </h1>
            <p class="text-gray-400">Upload your MP3 and scratch like a pro DJ</p>
        </header>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <!-- Turntable Section -->
            <div class="flex flex-col items-center space-y-6">
                <!-- Vinyl Record -->
                <div class="vinyl-container">
                    <div id="vinylRecord" class="vinyl-record">
                        <div class="vinyl-grooves"></div>
                        <div class="vinyl-label">
                            <span id="trackLabel">No Track</span>
                        </div>
                    </div>
                </div>

                <!-- Turntable Controls -->
                <div class="control-panel p-6 w-full max-w-md">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-2">
                            <div id="statusIndicator" class="status-indicator"></div>
                            <span id="statusText" class="text-sm">Stopped</span>
                        </div>
                        <div class="text-sm text-gray-400">
                            <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="bg-gray-700 rounded-full h-2 mb-4">
                        <div id="progressBar" class="progress-bar w-0"></div>
                    </div>

                    <!-- Control Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="playPauseBtn" class="btn px-6 py-2" disabled>
                            <span id="playPauseText">Play</span>
                        </button>
                        <button id="resetBtn" class="btn px-4 py-2" disabled>Reset</button>
                    </div>
                </div>
            </div>

            <!-- Upload and Waveform Section -->
            <div class="space-y-6">
                <!-- Upload Area -->
                <div id="uploadArea" class="upload-area p-8 text-center">
                    <div class="mb-4">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </div>
                    <p class="text-lg mb-2">Drop your MP3 file here</p>
                    <p class="text-gray-400 mb-4">or click to browse</p>
                    <input type="file" id="fileInput" accept=".mp3,audio/mpeg" class="hidden">
                    <button id="browseBtn" class="btn px-6 py-2">Browse Files</button>
                </div>

                <!-- Waveform Display -->
                <div class="control-panel p-6">
                    <h3 class="text-lg font-semibold mb-4">Waveform</h3>
                    <div id="waveformContainer" class="waveform-container">
                        <canvas id="waveformCanvas" class="w-full h-full"></canvas>
                    </div>
                </div>

                <!-- Settings Panel -->
                <div class="control-panel p-6">
                    <h3 class="text-lg font-semibold mb-4">Settings</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Scratch Sensitivity</label>
                            <input type="range" id="sensitivitySlider" min="0.1" max="3" step="0.1" value="1" 
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Vinyl Crackle</label>
                            <input type="range" id="crackleSlider" min="0" max="1" step="0.1" value="0.3" 
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/audio-engine.js"></script>
    <script src="js/vinyl-ui.js"></script>
    <script src="js/scratch-controller.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
