# Virtual Vinyl Turntable

A web-based virtual vinyl turntable that simulates real scratching behavior. Upload your own MP3 files and control playback via a draggable spinning vinyl UI element.

## Features

### Core Functionality
- **MP3 Upload**: Drag-and-drop or file input to load .mp3 files locally (no server upload required)
- **Virtual Vinyl UI**: Interactive SVG vinyl record with realistic rotation and draggable controls
- **Real-time Scratching**: Mouse/touch control for scratching with pitch and playback rate modulation
- **Audio Engine**: Web Audio API integration with AudioBufferSourceNode for high-quality audio processing

### Audio Features
- **Scratch Control**: Real-time pitch and playback rate changes based on vinyl rotation
- **Vinyl Crackle**: Simulated vinyl noise and crackle effects
- **Gesture Recognition**: Automatic detection of scratch patterns (baby scratch, transform, chirp)
- **Audio Effects**: Dynamic audio processing based on scratch intensity

### User Interface
- **Waveform Display**: Visual representation of the loaded audio track
- **Progress Tracking**: Real-time playback position with time display
- **Control Panel**: Play/pause, reset, and sensitivity controls
- **Settings**: Adjustable scratch sensitivity and crackle volume
- **Mobile Support**: Touch-optimized controls for mobile devices

### Advanced Features
- **Momentum Physics**: Realistic vinyl momentum when released after scratching
- **Visual Effects**: Dynamic glow, jitter, and color effects during scratching
- **Performance Metrics**: Tracking of scratch patterns and performance statistics
- **Local Storage**: Settings persistence across browser sessions
- **Keyboard Shortcuts**: Space (play/pause), R (reset), Arrow keys (sensitivity)

## Usage

1. **Load Audio**: 
   - Drag and drop an MP3 file onto the upload area, or
   - Click "Browse Files" to select an MP3 file

2. **Basic Playback**:
   - Click the "Play" button to start normal playback
   - The vinyl will rotate automatically during playback
   - Use "Reset" to return to the beginning

3. **Scratching**:
   - Click and drag the vinyl record to scratch
   - Rotation speed and direction control pitch and playback rate
   - Release to return to normal playback with momentum

4. **Settings**:
   - Adjust "Scratch Sensitivity" to control responsiveness
   - Modify "Vinyl Crackle" volume for authentic vinyl sound
   - Settings are automatically saved

## Technical Details

### Architecture
- **Audio Engine** (`audio-engine.js`): Web Audio API integration and audio processing
- **Vinyl UI** (`vinyl-ui.js`): Interactive vinyl record component with physics
- **Scratch Controller** (`scratch-controller.js`): Connects UI interactions to audio manipulation
- **Main App** (`main.js`): Application coordination and UI management

### Browser Compatibility
- Modern browsers with Web Audio API support
- Chrome, Firefox, Safari, Edge (latest versions)
- Mobile browsers with touch event support

### Performance
- Client-side audio processing (no server required)
- Optimized for real-time audio manipulation
- Efficient canvas-based waveform visualization
- Smooth 60fps animations and interactions

## Keyboard Shortcuts

- **Space**: Play/Pause
- **R**: Reset playback
- **↑/↓**: Adjust scratch sensitivity
- **Click + Drag**: Scratch the vinyl

## Development

### Running Locally
```bash
# Start a local HTTP server
python3 -m http.server 8000

# Open in browser
open http://localhost:8000
```

### File Structure
```
├── index.html              # Main HTML file
├── js/
│   ├── audio-engine.js     # Web Audio API integration
│   ├── vinyl-ui.js         # Vinyl record UI component
│   ├── scratch-controller.js # Scratch control logic
│   └── main.js             # Main application
└── README.md               # This file
```

### Technologies Used
- **HTML5**: Structure and layout
- **CSS3**: Styling, animations, and responsive design
- **JavaScript ES6+**: Application logic and interactions
- **Web Audio API**: Real-time audio processing and effects
- **Canvas API**: Waveform visualization
- **Tailwind CSS**: Utility-first CSS framework

## Features in Detail

### Scratch Techniques Supported
- **Baby Scratch**: Basic forward-backward motion
- **Transform**: Rapid stutter patterns
- **Chirp**: Quick forward scratches
- **Custom Patterns**: Free-form scratching with gesture recognition

### Audio Processing
- Real-time playback rate modulation
- Pitch shifting based on scratch speed
- Dynamic audio effects during scratching
- Vinyl crackle and noise simulation
- Smooth audio transitions and crossfading

### Visual Effects
- Realistic vinyl rotation with momentum
- Dynamic glow effects during scratching
- Visual jitter and analog-style effects
- Responsive waveform visualization
- Smooth CSS3 animations and transitions

## Browser Requirements

- Web Audio API support
- ES6+ JavaScript support
- Canvas 2D context support
- File API for MP3 loading
- Touch events (for mobile)

## License

This project is open source and available under the MIT License.
