<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Audio Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4ecdc4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #45b7aa;
        }
        .controls {
            margin: 20px 0;
        }
        label {
            display: block;
            margin: 10px 0 5px 0;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Audio Generator</h1>
        <p>Generate test audio files for the Virtual Vinyl Turntable</p>
        
        <div class="controls">
            <label for="frequency">Frequency (Hz):</label>
            <input type="number" id="frequency" value="440" min="100" max="2000">
            
            <label for="duration">Duration (seconds):</label>
            <input type="number" id="duration" value="10" min="1" max="60">
            
            <label for="waveform">Waveform:</label>
            <select id="waveform">
                <option value="sine">Sine Wave</option>
                <option value="square">Square Wave</option>
                <option value="sawtooth">Sawtooth Wave</option>
                <option value="triangle">Triangle Wave</option>
            </select>
            
            <label for="volume">Volume:</label>
            <input type="range" id="volume" min="0" max="1" step="0.1" value="0.5">
        </div>
        
        <button onclick="generateAudio()">Generate Audio</button>
        <button onclick="playPreview()">Preview</button>
        <button onclick="stopPreview()">Stop</button>
        
        <div id="status" style="margin-top: 20px; font-style: italic;"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f8f8; border-radius: 5px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Adjust the audio parameters above</li>
                <li>Click "Generate Audio" to create a test file</li>
                <li>The generated audio will be downloaded as a WAV file</li>
                <li>Use this file to test the Virtual Vinyl Turntable</li>
            </ol>
            <p><strong>Note:</strong> This generates WAV files. For MP3 testing, you'll need to convert the WAV file or use existing MP3 files.</p>
        </div>
    </div>

    <script>
        let audioContext;
        let currentSource;
        
        function initAudioContext() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            return audioContext;
        }
        
        function generateAudio() {
            const frequency = parseFloat(document.getElementById('frequency').value);
            const duration = parseFloat(document.getElementById('duration').value);
            const waveform = document.getElementById('waveform').value;
            const volume = parseFloat(document.getElementById('volume').value);
            
            const ctx = initAudioContext();
            const sampleRate = ctx.sampleRate;
            const numSamples = sampleRate * duration;
            
            // Create audio buffer
            const buffer = ctx.createBuffer(1, numSamples, sampleRate);
            const channelData = buffer.getChannelData(0);
            
            // Generate waveform
            for (let i = 0; i < numSamples; i++) {
                const t = i / sampleRate;
                const angle = 2 * Math.PI * frequency * t;
                
                let sample;
                switch (waveform) {
                    case 'sine':
                        sample = Math.sin(angle);
                        break;
                    case 'square':
                        sample = Math.sign(Math.sin(angle));
                        break;
                    case 'sawtooth':
                        sample = 2 * (t * frequency - Math.floor(t * frequency + 0.5));
                        break;
                    case 'triangle':
                        sample = 2 * Math.abs(2 * (t * frequency - Math.floor(t * frequency + 0.5))) - 1;
                        break;
                    default:
                        sample = Math.sin(angle);
                }
                
                channelData[i] = sample * volume;
            }
            
            // Convert to WAV and download
            const wavData = bufferToWav(buffer);
            const blob = new Blob([wavData], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-audio-${frequency}hz-${duration}s-${waveform}.wav`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            document.getElementById('status').textContent = `Generated ${duration}s ${waveform} wave at ${frequency}Hz`;
        }
        
        function playPreview() {
            stopPreview();
            
            const frequency = parseFloat(document.getElementById('frequency').value);
            const waveform = document.getElementById('waveform').value;
            const volume = parseFloat(document.getElementById('volume').value);
            
            const ctx = initAudioContext();
            
            const oscillator = ctx.createOscillator();
            const gainNode = ctx.createGain();
            
            oscillator.type = waveform;
            oscillator.frequency.setValueAtTime(frequency, ctx.currentTime);
            gainNode.gain.setValueAtTime(volume, ctx.currentTime);
            
            oscillator.connect(gainNode);
            gainNode.connect(ctx.destination);
            
            oscillator.start();
            currentSource = oscillator;
            
            document.getElementById('status').textContent = `Playing preview: ${frequency}Hz ${waveform} wave`;
        }
        
        function stopPreview() {
            if (currentSource) {
                currentSource.stop();
                currentSource = null;
                document.getElementById('status').textContent = 'Preview stopped';
            }
        }
        
        function bufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // Convert float samples to 16-bit PCM
            const channelData = buffer.getChannelData(0);
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }
    </script>
</body>
</html>
