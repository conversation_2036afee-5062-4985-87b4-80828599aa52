# Тестирование Virtual Vinyl Turntable

## Быстрый тест скретча

### 1. Подготовка тестового аудио
1. Откройте `http://localhost:8000/create-test-mp3.html`
2. Нажмите "Create Drum Beat (WAV)" для создания тестового файла
3. Скачается файл `drum-beat-20s.wav`

### 2. Тестирование основного приложения
1. Откройте `http://localhost:8000`
2. Если появится кнопка "Enable Audio" - нажмите её
3. Перетащите WAV файл в область загрузки или нажмите "Browse Files"
4. Дождитесь загрузки (статус "Ready")

### 3. Тестирование скретча
1. **Обычное воспроизведение**: Нажмите "Play" - винил должен вращаться
2. **Скретч**: 
   - Нажмите и удерживайте винил
   - Двигайте мышью по кругу
   - Должен слышаться звук скретча
   - Отпустите - воспроизведение продолжится

### 4. Проверка функций
- ✅ Загрузка MP3/WAV файлов
- ✅ Воспроизведение/пауза
- ✅ Визуальное вращение винила
- ✅ Скретч с аудио обратной связью
- ✅ Настройки чувствительности
- ✅ Эффекты треска винила

## Отладка проблем

### Если скретч не работает:
1. Откройте Developer Tools (F12)
2. Перейдите в Console
3. Ищите сообщения об ошибках
4. Проверьте, что аудио контекст активирован

### Если нет звука:
1. Проверьте, что браузер не заблокировал автовоспроизведение
2. Нажмите кнопку "Enable Audio" если она появилась
3. Убедитесь, что громкость не на нуле

### Если винил не вращается:
1. Проверьте консоль на ошибки JavaScript
2. Убедитесь, что файл загружен полностью
3. Попробуйте другой аудио файл

## Ожидаемое поведение скретча

### При захвате винила:
- Винил должен подсвечиваться
- Обычное воспроизведение останавливается
- Включается режим скретча

### При движении винила:
- Слышен звук скретча в реальном времени
- Скорость и направление влияют на высоту тона
- Визуальные эффекты (свечение, дрожание)

### При отпускании винила:
- Возвращается обычное воспроизведение
- Винил может продолжить вращение по инерции

## Технические детали

### Поддерживаемые форматы:
- MP3 (основной)
- WAV (для тестирования)

### Браузеры:
- Chrome (рекомендуется)
- Firefox
- Safari
- Edge

### Требования:
- Web Audio API
- File API
- Canvas 2D
- ES6+ JavaScript

## Горячие клавиши
- **Пробел**: Play/Pause
- **R**: Reset
- **↑/↓**: Настройка чувствительности

## Известные ограничения
- Работает только с локальными файлами
- Требует активации аудио контекста пользователем
- Лучше всего работает в Chrome
