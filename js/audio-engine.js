/**
 * Audio Engine for Virtual Vinyl Turntable
 * Handles Web Audio API integration, MP3 loading, and real-time audio manipulation
 * Based on professional algorithms from xwax DVS software
 */

class AudioEngine {
    constructor() {
        this.audioContext = null;
        this.audioBuffer = null;
        this.sourceNode = null;
        this.gainNode = null;
        this.analyserNode = null;
        this.scriptProcessor = null;

        // Playback state
        this.isPlaying = false;
        this.isPaused = false;
        this.startTime = 0;
        this.pauseTime = 0;
        this.playbackRate = 1.0;
        this.currentPosition = 0;

        // Professional scratch control (xwax-inspired)
        this.isScratchMode = false;
        this.scratchPosition = 0;
        this.targetPosition = null;
        this.lastScratchTime = 0;
        this.pitchFilter = new PitchFilter();
        this.syncPitch = 1.0;
        this.lastDifference = 0.0;

        // Audio interpolation buffer
        this.interpolationBuffer = new Float32Array(4);

        // Audio effects
        this.crackleGain = null;
        this.crackleBuffer = null;
        this.crackleSource = null;

        // Constants from xwax
        this.SYNC_TIME = 1.0 / 2; // time taken to reach sync
        this.SYNC_PITCH = 0.05; // don't sync at low pitches
        this.SKIP_THRESHOLD = 1.0 / 8; // before dropping audio
        this.VOLUME = 7.0 / 8; // base volume level

        // Callbacks
        this.onTimeUpdate = null;
        this.onLoadComplete = null;
        this.onPlayStateChange = null;

        this.initializeAudioContext();
        this.generateCrackleNoise();
    }
    
    async initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create audio nodes
            this.gainNode = this.audioContext.createGain();
            this.analyserNode = this.audioContext.createAnalyser();
            this.crackleGain = this.audioContext.createGain();
            
            // Configure analyser
            this.analyserNode.fftSize = 2048;
            this.analyserNode.smoothingTimeConstant = 0.8;
            
            // Connect nodes
            this.gainNode.connect(this.analyserNode);
            this.analyserNode.connect(this.audioContext.destination);
            this.crackleGain.connect(this.audioContext.destination);
            
            // Set initial crackle volume
            this.crackleGain.gain.value = 0.3;
            
            console.log('Audio context initialized successfully');
        } catch (error) {
            console.error('Failed to initialize audio context:', error);
        }
    }
    
    async loadAudioFile(file) {
        try {
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            const arrayBuffer = await file.arrayBuffer();
            this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            console.log('Audio file loaded successfully');
            console.log(`Duration: ${this.audioBuffer.duration.toFixed(2)}s`);
            console.log(`Sample Rate: ${this.audioBuffer.sampleRate}Hz`);
            
            if (this.onLoadComplete) {
                this.onLoadComplete(this.audioBuffer);
            }
            
            return this.audioBuffer;
        } catch (error) {
            console.error('Failed to load audio file:', error);
            throw error;
        }
    }
    
    play() {
        if (!this.audioBuffer) {
            console.warn('No audio buffer loaded');
            return;
        }

        if (this.isPlaying) {
            this.stop();
        }

        // Create new source node
        this.sourceNode = this.audioContext.createBufferSource();
        this.sourceNode.buffer = this.audioBuffer;

        // Apply professional sync pitch (xwax-style)
        const effectiveRate = this.playbackRate * this.syncPitch;
        this.sourceNode.playbackRate.value = effectiveRate;

        // Connect to audio graph
        this.sourceNode.connect(this.gainNode);

        // Handle playback end
        this.sourceNode.onended = () => {
            if (this.isPlaying) {
                this.stop();
            }
        };

        // Calculate start position
        const startOffset = this.isPaused ? this.pauseTime : 0;
        this.startTime = this.audioContext.currentTime - startOffset;

        // Start playback
        this.sourceNode.start(0, startOffset);
        this.isPlaying = true;
        this.isPaused = false;

        // Start time updates with professional sync
        this.startTimeUpdates();

        if (this.onPlayStateChange) {
            this.onPlayStateChange(true);
        }

        console.log('Playback started with professional sync');
    }
    
    pause() {
        if (!this.isPlaying) return;
        
        this.pauseTime = this.audioContext.currentTime - this.startTime;
        this.stop();
        this.isPaused = true;
        
        if (this.onPlayStateChange) {
            this.onPlayStateChange(false);
        }
        
        console.log('Playback paused');
    }
    
    stop() {
        if (this.sourceNode) {
            this.sourceNode.stop();
            this.sourceNode.disconnect();
            this.sourceNode = null;
        }
        
        this.isPlaying = false;
        this.stopTimeUpdates();
        
        if (this.onPlayStateChange) {
            this.onPlayStateChange(false);
        }
        
        console.log('Playback stopped');
    }
    
    reset() {
        this.stop();
        this.pauseTime = 0;
        this.isPaused = false;
        this.currentPosition = 0;
        this.playbackRate = 1.0;
        
        if (this.onTimeUpdate) {
            this.onTimeUpdate(0, this.audioBuffer ? this.audioBuffer.duration : 0);
        }
        
        console.log('Playback reset');
    }
    
    setPlaybackRate(rate) {
        this.playbackRate = Math.max(0.1, Math.min(4.0, rate));
        
        if (this.sourceNode && this.isPlaying) {
            this.sourceNode.playbackRate.value = this.playbackRate;
        }
    }
    
    setVolume(volume) {
        if (this.gainNode) {
            this.gainNode.gain.value = Math.max(0, Math.min(1, volume));
        }
    }
    
    setCrackleVolume(volume) {
        if (this.crackleGain) {
            this.crackleGain.gain.value = Math.max(0, Math.min(1, volume));
        }
    }
    
    getCurrentTime() {
        if (!this.audioBuffer) return 0;
        
        if (this.isPlaying) {
            return Math.min(
                (this.audioContext.currentTime - this.startTime) * this.playbackRate,
                this.audioBuffer.duration
            );
        } else if (this.isPaused) {
            return this.pauseTime;
        }
        
        return 0;
    }
    
    getDuration() {
        return this.audioBuffer ? this.audioBuffer.duration : 0;
    }
    
    getFrequencyData() {
        if (!this.analyserNode) return new Uint8Array(0);
        
        const bufferLength = this.analyserNode.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyserNode.getByteFrequencyData(dataArray);
        
        return dataArray;
    }
    
    getWaveformData() {
        if (!this.analyserNode) return new Uint8Array(0);
        
        const bufferLength = this.analyserNode.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyserNode.getByteTimeDomainData(dataArray);
        
        return dataArray;
    }
    
    startTimeUpdates() {
        this.stopTimeUpdates();

        this.timeUpdateInterval = setInterval(() => {
            if (this.isPlaying && this.onTimeUpdate) {
                // Apply professional synchronization
                this.retarget();

                const currentTime = this.getCurrentTime();
                const duration = this.getDuration();
                this.onTimeUpdate(currentTime, duration);

                // Update sync pitch gradually (xwax RC filter)
                const dt = 0.1; // 100ms update interval
                const SYNC_RC = 0.05;
                this.syncPitch += dt / (SYNC_RC + dt) * (1.0 - this.syncPitch);
            }
        }, 100);
    }
    
    stopTimeUpdates() {
        if (this.timeUpdateInterval) {
            clearInterval(this.timeUpdateInterval);
            this.timeUpdateInterval = null;
        }
    }
    
    generateCrackleNoise() {
        if (!this.audioContext) return;
        
        const bufferSize = this.audioContext.sampleRate * 2; // 2 seconds of noise
        this.crackleBuffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = this.crackleBuffer.getChannelData(0);
        
        for (let i = 0; i < bufferSize; i++) {
            // Generate vinyl crackle noise
            const noise = (Math.random() * 2 - 1) * 0.1;
            const pop = Math.random() < 0.001 ? (Math.random() * 2 - 1) * 0.3 : 0;
            output[i] = noise + pop;
        }
    }
    
    startCrackle() {
        if (!this.crackleBuffer || this.crackleSource) return;
        
        this.crackleSource = this.audioContext.createBufferSource();
        this.crackleSource.buffer = this.crackleBuffer;
        this.crackleSource.loop = true;
        this.crackleSource.connect(this.crackleGain);
        this.crackleSource.start();
    }
    
    stopCrackle() {
        if (this.crackleSource) {
            this.crackleSource.stop();
            this.crackleSource.disconnect();
            this.crackleSource = null;
        }
    }
    
    // Professional synchronization system (xwax-inspired)
    retarget() {
        if (this.targetPosition === null) return;

        const diff = this.getCurrentTime() - this.targetPosition;
        this.lastDifference = diff;

        // Jump if difference is too large (xwax SKIP_THRESHOLD)
        if (Math.abs(diff) > this.SKIP_THRESHOLD) {
            this.pauseTime = this.targetPosition;
            console.log(`Seek to new position ${this.targetPosition.toFixed(2)}s`);
        } else if (Math.abs(this.playbackRate) > this.SYNC_PITCH) {
            // Re-calculate sync pitch for smooth synchronization
            this.syncPitch = this.playbackRate / (diff / this.SYNC_TIME + this.playbackRate);
        }

        this.targetPosition = null;
    }

    // Scratch mode methods with professional algorithms
    enterScratchMode() {
        this.isScratchMode = true;
        this.scratchPosition = this.getCurrentTime();
        this.lastScratchTime = performance.now();
        this.pitchFilter.reset();
        this.targetPosition = null;

        if (this.isPlaying) {
            this.pause();
        }

        this.startCrackle();
    }

    exitScratchMode() {
        this.isScratchMode = false;
        this.stopCrackle();
        this.pitchFilter.reset();
        this.syncPitch = 1.0;

        // Resume normal playback if we were playing before
        if (this.isPaused) {
            this.play();
        }
    }
    
    updateScratchPosition(deltaAngle, sensitivity = 1.0) {
        if (!this.isScratchMode || !this.audioBuffer) return;

        const now = performance.now();
        const timeDelta = (now - this.lastScratchTime) / 1000;
        this.lastScratchTime = now;

        // Professional pitch calculation (xwax-inspired)
        const dx = (deltaAngle / (2 * Math.PI)) * sensitivity * 0.1;
        this.pitchFilter.addObservation(dx, timeDelta);

        // Get filtered pitch
        const filteredPitch = this.pitchFilter.getCurrentPitch();
        this.playbackRate = filteredPitch;

        // Update scratch position
        this.scratchPosition += dx;
        this.scratchPosition = Math.max(0, Math.min(this.audioBuffer.duration, this.scratchPosition));

        // Set target position for synchronization
        this.targetPosition = this.scratchPosition;

        // Update audio playback position
        this.pauseTime = this.scratchPosition;

        if (this.onTimeUpdate) {
            this.onTimeUpdate(this.scratchPosition, this.audioBuffer.duration);
        }

        // Play scratch snippet with professional quality
        if (Math.abs(deltaAngle) > 0.005) {
            this.playScratchSnippetPro(this.scratchPosition, filteredPitch * 10); // Scale for audible effect
        }
    }
    
    playScratchSnippet(position, rate) {
        if (!this.audioBuffer) return;

        try {
            // Stop previous scratch snippet
            if (this.scratchSourceNode) {
                try {
                    this.scratchSourceNode.stop();
                    this.scratchSourceNode.disconnect();
                } catch (e) {
                    // Ignore errors from already stopped nodes
                }
            }

            // Create new source for scratch snippet
            this.scratchSourceNode = this.audioContext.createBufferSource();
            this.scratchSourceNode.buffer = this.audioBuffer;

            // Set playback rate with bounds checking
            const clampedRate = Math.max(-4, Math.min(4, rate || 1));
            this.scratchSourceNode.playbackRate.value = Math.abs(clampedRate);

            // Connect to audio graph
            const scratchGain = this.audioContext.createGain();
            scratchGain.gain.value = 0.8;
            this.scratchSourceNode.connect(scratchGain);
            scratchGain.connect(this.gainNode);

            // Calculate start position and duration
            const startPos = Math.max(0, Math.min(this.audioBuffer.duration - 0.1, position));
            const snippetDuration = 0.05; // Shorter snippets for better responsiveness

            // Start playback
            this.scratchSourceNode.start(0, startPos, snippetDuration);

            // Clean up after snippet ends
            this.scratchSourceNode.onended = () => {
                try {
                    if (this.scratchSourceNode) {
                        this.scratchSourceNode.disconnect();
                        this.scratchSourceNode = null;
                    }
                    scratchGain.disconnect();
                } catch (e) {
                    // Ignore cleanup errors
                }
            };

        } catch (error) {
            console.warn('Error playing scratch snippet:', error);
        }
    }

    // Professional scratch snippet with high-quality interpolation
    playScratchSnippetPro(position, rate) {
        if (!this.audioBuffer) return;

        try {
            // Stop previous scratch snippet
            if (this.scratchSourceNode) {
                try {
                    this.scratchSourceNode.stop();
                    this.scratchSourceNode.disconnect();
                } catch (e) {
                    // Ignore errors from already stopped nodes
                }
            }

            // Create AudioWorklet or ScriptProcessor for real-time processing
            if (this.audioContext.audioWorklet) {
                // Use AudioWorklet for better performance (if available)
                this.createAudioWorkletScratch(position, rate);
            } else {
                // Fallback to ScriptProcessor
                this.createScriptProcessorScratch(position, rate);
            }

        } catch (error) {
            console.warn('Error playing professional scratch snippet:', error);
            // Fallback to simple method
            this.playScratchSnippet(position, rate);
        }
    }

    createScriptProcessorScratch(position, rate) {
        // Create a short-lived ScriptProcessor for scratch audio
        const bufferSize = 512;
        const processor = this.audioContext.createScriptProcessor(bufferSize, 0, 2);

        let samplePosition = position * this.audioBuffer.sampleRate;
        const step = Math.abs(rate) * this.audioBuffer.sampleRate / this.audioContext.sampleRate;
        let samplesProcessed = 0;
        const maxSamples = bufferSize * 2; // Short snippet

        processor.onaudioprocess = (event) => {
            const outputBuffer = event.outputBuffer;
            const outputData = [
                outputBuffer.getChannelData(0),
                outputBuffer.getChannelData(1)
            ];

            for (let i = 0; i < bufferSize; i++) {
                if (samplesProcessed >= maxSamples) {
                    // End of snippet
                    outputData[0][i] = 0;
                    outputData[1][i] = 0;
                    continue;
                }

                // Get interpolated sample
                const sampleIndex = Math.floor(samplePosition);
                const fraction = samplePosition - sampleIndex;

                let leftSample = 0, rightSample = 0;

                if (sampleIndex >= 0 && sampleIndex < this.audioBuffer.length - 3) {
                    // Cubic interpolation for both channels
                    const leftData = this.audioBuffer.getChannelData(0);
                    const rightData = this.audioBuffer.numberOfChannels > 1 ?
                        this.audioBuffer.getChannelData(1) : leftData;

                    const leftY = [
                        leftData[sampleIndex],
                        leftData[sampleIndex + 1],
                        leftData[sampleIndex + 2],
                        leftData[sampleIndex + 3]
                    ];

                    const rightY = [
                        rightData[sampleIndex],
                        rightData[sampleIndex + 1],
                        rightData[sampleIndex + 2],
                        rightData[sampleIndex + 3]
                    ];

                    leftSample = AudioInterpolator.cubicInterpolate(leftY, fraction);
                    rightSample = AudioInterpolator.cubicInterpolate(rightY, fraction);
                }

                // Apply volume and direction
                const volume = 0.7 * Math.min(1.0, Math.abs(rate));
                outputData[0][i] = leftSample * volume;
                outputData[1][i] = rightSample * volume;

                // Move to next sample
                samplePosition += rate > 0 ? step : -step;
                samplesProcessed++;
            }

            // Clean up after processing
            if (samplesProcessed >= maxSamples) {
                processor.disconnect();
                this.scratchSourceNode = null;
            }
        };

        // Connect and start
        processor.connect(this.gainNode);
        this.scratchSourceNode = processor;
    }
}

/**
 * Professional Pitch Filter based on xwax algorithms
 * Uses Kalman-like filtering for smooth pitch detection
 */
class PitchFilter {
    constructor(dt = 1.0 / 44100) {
        // Filter constants from xwax
        this.ALPHA = 1.0 / 512;
        this.BETA = this.ALPHA / 256;

        this.dt = dt;
        this.x = 0.0; // position
        this.v = 0.0; // velocity (pitch)
    }

    // Input an observation: in the last dt seconds, position moved by dx
    addObservation(dx, dt = null) {
        if (dt !== null) this.dt = dt;

        const predicted_x = this.x + this.v * this.dt;
        const predicted_v = this.v;
        const residual_x = dx - predicted_x;

        this.x = predicted_x + residual_x * this.ALPHA;
        this.v = predicted_v + residual_x * this.BETA / this.dt;
        this.x -= dx; // relative to previous
    }

    // Get current filtered pitch
    getCurrentPitch() {
        return this.v;
    }

    reset() {
        this.x = 0.0;
        this.v = 0.0;
    }
}

/**
 * Professional Audio Interpolation
 * Cubic interpolation for high-quality audio resampling
 */
class AudioInterpolator {
    // Cubic interpolation of 4 samples at position mu (0-1)
    static cubicInterpolate(y, mu) {
        const mu2 = mu * mu;
        const a0 = y[3] - y[2] - y[0] + y[1];
        const a1 = y[0] - y[1] - a0;
        const a2 = y[2] - y[0];
        const a3 = y[1];

        return (mu * mu2 * a0) + (mu2 * a1) + (mu * a2) + a3;
    }

    // Random dither for better audio quality
    static dither() {
        // Simple linear congruential generator for dither
        AudioInterpolator._ditherState = (AudioInterpolator._ditherState * 1664525 + 1013904223) % 4294967296;
        return (AudioInterpolator._ditherState / 4294967296) - 0.5;
    }

    // Resample audio buffer with professional quality
    static resampleBuffer(inputBuffer, outputBuffer, startPosition, pitch, startVolume, endVolume) {
        const inputLength = inputBuffer.length;
        const outputLength = outputBuffer.length;
        const channels = Math.min(inputBuffer.numberOfChannels, 2);

        let sample = startPosition * inputBuffer.sampleRate;
        const step = pitch * inputBuffer.sampleRate / 44100; // Assume 44.1kHz output

        const volume = startVolume;
        const volumeStep = (endVolume - startVolume) / outputLength;

        for (let i = 0; i < outputLength; i++) {
            const sampleIndex = Math.floor(sample);
            const fraction = sample - sampleIndex;

            // Get 4 samples for cubic interpolation
            const samples = new Array(channels);
            for (let ch = 0; ch < channels; ch++) {
                const channelData = inputBuffer.getChannelData(ch);
                const y = new Array(4);

                for (let j = 0; j < 4; j++) {
                    const idx = sampleIndex - 1 + j;
                    y[j] = (idx >= 0 && idx < inputLength) ? channelData[idx] : 0;
                }

                samples[ch] = this.cubicInterpolate(y, fraction);
            }

            // Apply volume and dither
            const currentVolume = volume + volumeStep * i;
            for (let ch = 0; ch < channels; ch++) {
                const value = samples[ch] * currentVolume + this.dither() * 0.0001;
                outputBuffer[i * 2 + ch] = Math.max(-1, Math.min(1, value));
            }

            // Fill second channel if mono input
            if (channels === 1) {
                outputBuffer[i * 2 + 1] = outputBuffer[i * 2];
            }

            sample += step;
        }

        return (sample - startPosition * inputBuffer.sampleRate) / inputBuffer.sampleRate;
    }
}

// Initialize dither state
AudioInterpolator._ditherState = 0xbeefface;

// Export for use in other modules
window.AudioEngine = AudioEngine;
window.PitchFilter = PitchFilter;
window.AudioInterpolator = AudioInterpolator;
