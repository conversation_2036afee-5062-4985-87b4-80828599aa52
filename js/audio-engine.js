/**
 * Audio Engine for Virtual Vinyl Turntable
 * Handles Web Audio API integration, MP3 loading, and real-time audio manipulation
 */

class AudioEngine {
    constructor() {
        this.audioContext = null;
        this.audioBuffer = null;
        this.sourceNode = null;
        this.gainNode = null;
        this.analyserNode = null;
        this.scriptProcessor = null;
        
        // Playback state
        this.isPlaying = false;
        this.isPaused = false;
        this.startTime = 0;
        this.pauseTime = 0;
        this.playbackRate = 1.0;
        this.currentPosition = 0;
        
        // Scratch control
        this.isScratchMode = false;
        this.scratchPosition = 0;
        this.lastScratchTime = 0;
        
        // Audio effects
        this.crackleGain = null;
        this.crackleBuffer = null;
        this.crackleSource = null;
        
        // Callbacks
        this.onTimeUpdate = null;
        this.onLoadComplete = null;
        this.onPlayStateChange = null;
        
        this.initializeAudioContext();
        this.generateCrackleNoise();
    }
    
    async initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create audio nodes
            this.gainNode = this.audioContext.createGain();
            this.analyserNode = this.audioContext.createAnalyser();
            this.crackleGain = this.audioContext.createGain();
            
            // Configure analyser
            this.analyserNode.fftSize = 2048;
            this.analyserNode.smoothingTimeConstant = 0.8;
            
            // Connect nodes
            this.gainNode.connect(this.analyserNode);
            this.analyserNode.connect(this.audioContext.destination);
            this.crackleGain.connect(this.audioContext.destination);
            
            // Set initial crackle volume
            this.crackleGain.gain.value = 0.3;
            
            console.log('Audio context initialized successfully');
        } catch (error) {
            console.error('Failed to initialize audio context:', error);
        }
    }
    
    async loadAudioFile(file) {
        try {
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            const arrayBuffer = await file.arrayBuffer();
            this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            console.log('Audio file loaded successfully');
            console.log(`Duration: ${this.audioBuffer.duration.toFixed(2)}s`);
            console.log(`Sample Rate: ${this.audioBuffer.sampleRate}Hz`);
            
            if (this.onLoadComplete) {
                this.onLoadComplete(this.audioBuffer);
            }
            
            return this.audioBuffer;
        } catch (error) {
            console.error('Failed to load audio file:', error);
            throw error;
        }
    }
    
    play() {
        if (!this.audioBuffer) {
            console.warn('No audio buffer loaded');
            return;
        }
        
        if (this.isPlaying) {
            this.stop();
        }
        
        // Create new source node
        this.sourceNode = this.audioContext.createBufferSource();
        this.sourceNode.buffer = this.audioBuffer;
        this.sourceNode.playbackRate.value = this.playbackRate;
        
        // Connect to audio graph
        this.sourceNode.connect(this.gainNode);
        
        // Handle playback end
        this.sourceNode.onended = () => {
            if (this.isPlaying) {
                this.stop();
            }
        };
        
        // Calculate start position
        const startOffset = this.isPaused ? this.pauseTime : 0;
        this.startTime = this.audioContext.currentTime - startOffset;
        
        // Start playback
        this.sourceNode.start(0, startOffset);
        this.isPlaying = true;
        this.isPaused = false;
        
        // Start time updates
        this.startTimeUpdates();
        
        if (this.onPlayStateChange) {
            this.onPlayStateChange(true);
        }
        
        console.log('Playback started');
    }
    
    pause() {
        if (!this.isPlaying) return;
        
        this.pauseTime = this.audioContext.currentTime - this.startTime;
        this.stop();
        this.isPaused = true;
        
        if (this.onPlayStateChange) {
            this.onPlayStateChange(false);
        }
        
        console.log('Playback paused');
    }
    
    stop() {
        if (this.sourceNode) {
            this.sourceNode.stop();
            this.sourceNode.disconnect();
            this.sourceNode = null;
        }
        
        this.isPlaying = false;
        this.stopTimeUpdates();
        
        if (this.onPlayStateChange) {
            this.onPlayStateChange(false);
        }
        
        console.log('Playback stopped');
    }
    
    reset() {
        this.stop();
        this.pauseTime = 0;
        this.isPaused = false;
        this.currentPosition = 0;
        this.playbackRate = 1.0;
        
        if (this.onTimeUpdate) {
            this.onTimeUpdate(0, this.audioBuffer ? this.audioBuffer.duration : 0);
        }
        
        console.log('Playback reset');
    }
    
    setPlaybackRate(rate) {
        this.playbackRate = Math.max(0.1, Math.min(4.0, rate));
        
        if (this.sourceNode && this.isPlaying) {
            this.sourceNode.playbackRate.value = this.playbackRate;
        }
    }
    
    setVolume(volume) {
        if (this.gainNode) {
            this.gainNode.gain.value = Math.max(0, Math.min(1, volume));
        }
    }
    
    setCrackleVolume(volume) {
        if (this.crackleGain) {
            this.crackleGain.gain.value = Math.max(0, Math.min(1, volume));
        }
    }
    
    getCurrentTime() {
        if (!this.audioBuffer) return 0;
        
        if (this.isPlaying) {
            return Math.min(
                (this.audioContext.currentTime - this.startTime) * this.playbackRate,
                this.audioBuffer.duration
            );
        } else if (this.isPaused) {
            return this.pauseTime;
        }
        
        return 0;
    }
    
    getDuration() {
        return this.audioBuffer ? this.audioBuffer.duration : 0;
    }
    
    getFrequencyData() {
        if (!this.analyserNode) return new Uint8Array(0);
        
        const bufferLength = this.analyserNode.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyserNode.getByteFrequencyData(dataArray);
        
        return dataArray;
    }
    
    getWaveformData() {
        if (!this.analyserNode) return new Uint8Array(0);
        
        const bufferLength = this.analyserNode.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyserNode.getByteTimeDomainData(dataArray);
        
        return dataArray;
    }
    
    startTimeUpdates() {
        this.stopTimeUpdates();
        
        this.timeUpdateInterval = setInterval(() => {
            if (this.isPlaying && this.onTimeUpdate) {
                const currentTime = this.getCurrentTime();
                const duration = this.getDuration();
                this.onTimeUpdate(currentTime, duration);
            }
        }, 100);
    }
    
    stopTimeUpdates() {
        if (this.timeUpdateInterval) {
            clearInterval(this.timeUpdateInterval);
            this.timeUpdateInterval = null;
        }
    }
    
    generateCrackleNoise() {
        if (!this.audioContext) return;
        
        const bufferSize = this.audioContext.sampleRate * 2; // 2 seconds of noise
        this.crackleBuffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = this.crackleBuffer.getChannelData(0);
        
        for (let i = 0; i < bufferSize; i++) {
            // Generate vinyl crackle noise
            const noise = (Math.random() * 2 - 1) * 0.1;
            const pop = Math.random() < 0.001 ? (Math.random() * 2 - 1) * 0.3 : 0;
            output[i] = noise + pop;
        }
    }
    
    startCrackle() {
        if (!this.crackleBuffer || this.crackleSource) return;
        
        this.crackleSource = this.audioContext.createBufferSource();
        this.crackleSource.buffer = this.crackleBuffer;
        this.crackleSource.loop = true;
        this.crackleSource.connect(this.crackleGain);
        this.crackleSource.start();
    }
    
    stopCrackle() {
        if (this.crackleSource) {
            this.crackleSource.stop();
            this.crackleSource.disconnect();
            this.crackleSource = null;
        }
    }
    
    // Scratch mode methods
    enterScratchMode() {
        this.isScratchMode = true;
        this.scratchPosition = this.getCurrentTime();
        this.lastScratchTime = performance.now();
        
        if (this.isPlaying) {
            this.pause();
        }
        
        this.startCrackle();
    }
    
    exitScratchMode() {
        this.isScratchMode = false;
        this.stopCrackle();
        
        // Resume normal playback if we were playing before
        if (this.isPaused) {
            this.play();
        }
    }
    
    updateScratchPosition(deltaAngle, sensitivity = 1.0) {
        if (!this.isScratchMode || !this.audioBuffer) return;

        const now = performance.now();
        const timeDelta = (now - this.lastScratchTime) / 1000;
        this.lastScratchTime = now;

        // Convert angle delta to time delta - make it more responsive
        const timeDeltaFromAngle = (deltaAngle / (2 * Math.PI)) * sensitivity * 2.0; // Increased multiplier

        // Update scratch position
        this.scratchPosition += timeDeltaFromAngle;
        this.scratchPosition = Math.max(0, Math.min(this.audioBuffer.duration, this.scratchPosition));

        // Calculate playback rate based on scratch speed
        const scratchSpeed = Math.abs(timeDeltaFromAngle) / Math.max(timeDelta, 0.001);
        const rate = Math.sign(timeDeltaFromAngle) * Math.min(scratchSpeed * 5, 3.0); // Adjusted for better response

        // Update audio playback
        this.pauseTime = this.scratchPosition;

        if (this.onTimeUpdate) {
            this.onTimeUpdate(this.scratchPosition, this.audioBuffer.duration);
        }

        // Always play scratch snippet when moving
        if (Math.abs(deltaAngle) > 0.01) {
            this.playScratchSnippet(this.scratchPosition, rate);
        }
    }
    
    playScratchSnippet(position, rate) {
        if (!this.audioBuffer) return;

        try {
            // Stop previous scratch snippet
            if (this.scratchSourceNode) {
                try {
                    this.scratchSourceNode.stop();
                    this.scratchSourceNode.disconnect();
                } catch (e) {
                    // Ignore errors from already stopped nodes
                }
            }

            // Create new source for scratch snippet
            this.scratchSourceNode = this.audioContext.createBufferSource();
            this.scratchSourceNode.buffer = this.audioBuffer;

            // Set playback rate with bounds checking
            const clampedRate = Math.max(-4, Math.min(4, rate || 1));
            this.scratchSourceNode.playbackRate.value = Math.abs(clampedRate);

            // Connect to audio graph
            const scratchGain = this.audioContext.createGain();
            scratchGain.gain.value = 0.8;
            this.scratchSourceNode.connect(scratchGain);
            scratchGain.connect(this.gainNode);

            // Calculate start position and duration
            const startPos = Math.max(0, Math.min(this.audioBuffer.duration - 0.1, position));
            const snippetDuration = 0.05; // Shorter snippets for better responsiveness

            // Start playback
            this.scratchSourceNode.start(0, startPos, snippetDuration);

            // Clean up after snippet ends
            this.scratchSourceNode.onended = () => {
                try {
                    if (this.scratchSourceNode) {
                        this.scratchSourceNode.disconnect();
                        this.scratchSourceNode = null;
                    }
                    scratchGain.disconnect();
                } catch (e) {
                    // Ignore cleanup errors
                }
            };

        } catch (error) {
            console.warn('Error playing scratch snippet:', error);
        }
    }
}

// Export for use in other modules
window.AudioEngine = AudioEngine;
