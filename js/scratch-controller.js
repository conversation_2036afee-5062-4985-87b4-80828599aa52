/**
 * Scratch Controller for Virtual Vinyl Turntable
 * Connects vinyl UI interactions with audio engine for real-time scratching
 */

class ScratchController {
    constructor(audioEngine, vinylUI) {
        this.audioEngine = audioEngine;
        this.vinylUI = vinylUI;
        
        // Scratch state
        this.isScratchActive = false;
        this.scratchSensitivity = 1.0;
        this.lastScratchTime = 0;
        this.scratchBuffer = [];
        this.scratchBufferSize = 10;
        
        // Playback state before scratch
        this.wasPlayingBeforeScratch = false;
        this.playbackRateBeforeScratch = 1.0;
        
        // Scratch patterns and gestures
        this.gestureRecognizer = new ScratchGestureRecognizer();
        this.scratchPresets = {
            babyScratch: { pattern: 'forward-back', speed: 'medium' },
            transform: { pattern: 'stutter', speed: 'fast' },
            chirp: { pattern: 'quick-forward', speed: 'very-fast' }
        };
        
        // Performance monitoring
        this.performanceMetrics = {
            scratchCount: 0,
            totalScratchTime: 0,
            averageScratchSpeed: 0
        };
        
        this.initializeCallbacks();
    }
    
    initializeCallbacks() {
        // Set up vinyl UI callbacks
        this.vinylUI.onGrab = () => this.startScratch();
        this.vinylUI.onRelease = () => this.endScratch();
        this.vinylUI.onRotate = (angleDelta, angularVelocity, sensitivity) => {
            this.updateScratch(angleDelta, angularVelocity, sensitivity);
        };
        this.vinylUI.onSpinStart = () => this.onScratchStart();
        this.vinylUI.onSpinStop = () => this.onScratchEnd();
    }
    
    startScratch() {
        if (this.isScratchActive) return;

        this.isScratchActive = true;
        this.lastScratchTime = performance.now();

        // Store current playback state
        this.wasPlayingBeforeScratch = this.audioEngine.isPlaying;
        this.playbackRateBeforeScratch = this.audioEngine.playbackRate;

        // Enter scratch mode in audio engine
        this.audioEngine.enterScratchMode();

        // Visual feedback
        this.vinylUI.addScratchEffect();
        this.vinylUI.addGlow();

        // Reset scratch buffer
        this.scratchBuffer = [];

        // Start gesture recognition
        this.gestureRecognizer.startRecording();

        // Update UI to show scratch mode
        const statusText = document.getElementById('statusText');
        if (statusText) {
            statusText.textContent = 'Scratching';
        }

        console.log('Scratch mode activated - audio buffer available:', !!this.audioEngine.audioBuffer);
    }
    
    endScratch() {
        if (!this.isScratchActive) return;

        this.isScratchActive = false;

        // Exit scratch mode in audio engine
        this.audioEngine.exitScratchMode();

        // Visual feedback
        this.vinylUI.removeScratchEffect();
        this.vinylUI.removeGlow();

        // Resume playback if it was playing before
        if (this.wasPlayingBeforeScratch) {
            setTimeout(() => {
                this.audioEngine.play();
            }, 100); // Small delay to avoid audio glitches
        }

        // End gesture recognition and analyze
        const gesture = this.gestureRecognizer.endRecording();
        this.analyzeGesture(gesture);

        // Update performance metrics
        this.updatePerformanceMetrics();

        // Update UI
        const statusText = document.getElementById('statusText');
        if (statusText) {
            statusText.textContent = this.wasPlayingBeforeScratch ? 'Playing' : 'Paused';
        }

        console.log('Scratch mode deactivated');
    }
    
    updateScratch(angleDelta, angularVelocity, sensitivity) {
        if (!this.isScratchActive) return;

        const now = performance.now();
        const timeDelta = (now - this.lastScratchTime) / 1000;
        this.lastScratchTime = now;

        // Apply sensitivity multiplier
        const adjustedAngleDelta = angleDelta * this.scratchSensitivity * sensitivity;

        // Debug logging (only for significant movements)
        if (Math.abs(adjustedAngleDelta) > 0.5) {
            console.log('Large scratch movement:', {
                angleDelta: angleDelta.toFixed(4),
                angularVelocity: angularVelocity.toFixed(4),
                adjustedAngleDelta: adjustedAngleDelta.toFixed(4)
            });
        }

        // Update audio engine scratch position
        this.audioEngine.updateScratchPosition(adjustedAngleDelta, this.scratchSensitivity);

        // Record scratch data for gesture recognition
        this.recordScratchData(adjustedAngleDelta, angularVelocity, timeDelta);

        // Apply real-time audio effects based on scratch intensity
        this.applyDynamicEffects(angularVelocity);
    }
    
    recordScratchData(angleDelta, angularVelocity, timeDelta) {
        const scratchData = {
            angleDelta,
            angularVelocity,
            timeDelta,
            timestamp: performance.now(),
            intensity: Math.abs(angularVelocity)
        };
        
        // Add to scratch buffer
        this.scratchBuffer.push(scratchData);
        
        // Keep buffer size manageable
        if (this.scratchBuffer.length > this.scratchBufferSize) {
            this.scratchBuffer.shift();
        }
        
        // Send to gesture recognizer
        this.gestureRecognizer.addDataPoint(scratchData);
    }
    
    applyDynamicEffects(angularVelocity) {
        const intensity = Math.abs(angularVelocity);

        // Adjust crackle volume based on scratch intensity
        const crackleVolume = Math.min(intensity * 0.1, 0.8);
        this.audioEngine.setCrackleVolume(crackleVolume);

        // Visual effects based on intensity
        if (intensity > 5) {
            this.vinylUI.vinylElement.style.filter = 'brightness(1.3) contrast(1.2) saturate(1.1)';
            this.vinylUI.vinylElement.classList.add('scratching');
        } else if (intensity > 2) {
            this.vinylUI.vinylElement.style.filter = 'brightness(1.2) contrast(1.1)';
            this.vinylUI.vinylElement.classList.remove('scratching');
        } else {
            this.vinylUI.vinylElement.style.filter = 'brightness(1.1)';
            this.vinylUI.vinylElement.classList.remove('scratching');
        }

        // Add screen shake effect for intense scratching
        if (intensity > 8) {
            document.body.style.transform = `translate(${Math.random() * 2 - 1}px, ${Math.random() * 2 - 1}px)`;
            setTimeout(() => {
                document.body.style.transform = 'none';
            }, 50);
        }
    }
    
    onScratchStart() {
        // Additional effects when scratch motion begins
        this.performanceMetrics.scratchCount++;
        
        // Trigger visual pulse effect
        this.vinylUI.vinylElement.style.transition = 'transform 0.1s ease-out';
        this.vinylUI.vinylElement.style.transform += ' scale(1.02)';
        
        setTimeout(() => {
            this.vinylUI.vinylElement.style.transform = this.vinylUI.vinylElement.style.transform.replace(' scale(1.02)', '');
        }, 100);
    }
    
    onScratchEnd() {
        // Effects when scratch motion ends
        this.vinylUI.vinylElement.style.filter = 'none';
        this.audioEngine.setCrackleVolume(0.3); // Reset to default
    }
    
    analyzeGesture(gesture) {
        if (!gesture || gesture.dataPoints.length < 3) return;
        
        // Analyze the gesture pattern
        const pattern = this.classifyGesturePattern(gesture);
        const speed = this.calculateGestureSpeed(gesture);
        const complexity = this.calculateGestureComplexity(gesture);
        
        console.log('Gesture analyzed:', { pattern, speed, complexity });
        
        // Trigger appropriate effects or feedback
        this.triggerGestureEffects(pattern, speed, complexity);
    }
    
    classifyGesturePattern(gesture) {
        const dataPoints = gesture.dataPoints;
        let forwardCount = 0;
        let backwardCount = 0;
        let directionChanges = 0;
        let lastDirection = 0;
        
        for (const point of dataPoints) {
            const direction = Math.sign(point.angularVelocity);
            
            if (direction > 0) forwardCount++;
            else if (direction < 0) backwardCount++;
            
            if (direction !== lastDirection && lastDirection !== 0) {
                directionChanges++;
            }
            lastDirection = direction;
        }
        
        // Classify based on direction changes and balance
        if (directionChanges > dataPoints.length * 0.5) {
            return 'stutter'; // Rapid direction changes
        } else if (forwardCount > backwardCount * 2) {
            return 'forward-sweep';
        } else if (backwardCount > forwardCount * 2) {
            return 'backward-sweep';
        } else if (Math.abs(forwardCount - backwardCount) < dataPoints.length * 0.2) {
            return 'baby-scratch'; // Balanced forward/backward
        } else {
            return 'transform';
        }
    }
    
    calculateGestureSpeed(gesture) {
        const speeds = gesture.dataPoints.map(p => Math.abs(p.angularVelocity));
        const avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
        
        if (avgSpeed > 10) return 'very-fast';
        else if (avgSpeed > 5) return 'fast';
        else if (avgSpeed > 2) return 'medium';
        else return 'slow';
    }
    
    calculateGestureComplexity(gesture) {
        const dataPoints = gesture.dataPoints;
        let complexity = 0;
        
        // Calculate based on velocity variations
        for (let i = 1; i < dataPoints.length; i++) {
            const velocityChange = Math.abs(dataPoints[i].angularVelocity - dataPoints[i-1].angularVelocity);
            complexity += velocityChange;
        }
        
        return complexity / dataPoints.length;
    }
    
    triggerGestureEffects(pattern, speed, complexity) {
        // Visual feedback based on gesture
        const effectDuration = speed === 'very-fast' ? 200 : speed === 'fast' ? 300 : 500;
        
        // Color effect based on pattern
        let effectColor = '#4ecdc4'; // Default cyan
        if (pattern === 'stutter') effectColor = '#ff6b6b'; // Red for stutter
        else if (pattern === 'transform') effectColor = '#ffd93d'; // Yellow for transform
        
        // Apply temporary glow effect
        this.vinylUI.vinylElement.style.boxShadow = `0 0 30px ${effectColor}, 0 10px 30px rgba(0, 0, 0, 0.5)`;
        
        setTimeout(() => {
            this.vinylUI.removeGlow();
        }, effectDuration);
    }
    
    updatePerformanceMetrics() {
        const scratchDuration = this.gestureRecognizer.getLastGestureDuration();
        this.performanceMetrics.totalScratchTime += scratchDuration;
        
        // Calculate average scratch speed
        if (this.scratchBuffer.length > 0) {
            const avgSpeed = this.scratchBuffer.reduce((sum, data) => sum + Math.abs(data.angularVelocity), 0) / this.scratchBuffer.length;
            this.performanceMetrics.averageScratchSpeed = avgSpeed;
        }
    }
    
    // Public methods
    setSensitivity(sensitivity) {
        this.scratchSensitivity = Math.max(0.1, Math.min(3.0, sensitivity));
        this.vinylUI.setSensitivity(sensitivity);
    }
    
    getSensitivity() {
        return this.scratchSensitivity;
    }
    
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    
    reset() {
        this.isScratchActive = false;
        this.scratchBuffer = [];
        this.performanceMetrics = {
            scratchCount: 0,
            totalScratchTime: 0,
            averageScratchSpeed: 0
        };
        this.gestureRecognizer.reset();
    }
}

/**
 * Gesture Recognition for Scratch Patterns
 */
class ScratchGestureRecognizer {
    constructor() {
        this.isRecording = false;
        this.currentGesture = null;
        this.gestureHistory = [];
        this.maxHistorySize = 50;
    }
    
    startRecording() {
        this.isRecording = true;
        this.currentGesture = {
            startTime: performance.now(),
            endTime: null,
            dataPoints: []
        };
    }
    
    addDataPoint(data) {
        if (!this.isRecording || !this.currentGesture) return;
        
        this.currentGesture.dataPoints.push({
            ...data,
            relativeTime: performance.now() - this.currentGesture.startTime
        });
    }
    
    endRecording() {
        if (!this.isRecording || !this.currentGesture) return null;
        
        this.isRecording = false;
        this.currentGesture.endTime = performance.now();
        
        // Add to history
        this.gestureHistory.push(this.currentGesture);
        
        // Maintain history size
        if (this.gestureHistory.length > this.maxHistorySize) {
            this.gestureHistory.shift();
        }
        
        const gesture = this.currentGesture;
        this.currentGesture = null;
        
        return gesture;
    }
    
    getLastGestureDuration() {
        if (this.gestureHistory.length === 0) return 0;
        
        const lastGesture = this.gestureHistory[this.gestureHistory.length - 1];
        return lastGesture.endTime - lastGesture.startTime;
    }
    
    reset() {
        this.isRecording = false;
        this.currentGesture = null;
        this.gestureHistory = [];
    }
}

// Export for use in other modules
window.ScratchController = ScratchController;
window.ScratchGestureRecognizer = ScratchGestureRecognizer;
