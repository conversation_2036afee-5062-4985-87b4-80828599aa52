/**
 * Vinyl UI Component for Virtual Vinyl Turntable
 * Handles the visual vinyl record, rotation animations, and user interactions
 */

class VinylUI {
    constructor(vinylElement, options = {}) {
        this.vinylElement = vinylElement;
        this.labelElement = vinylElement.querySelector('.vinyl-label span');
        
        // Configuration
        this.options = {
            rotationSpeed: 33.33, // RPM for normal playback
            sensitivity: 1.0,
            smoothing: 0.1,
            ...options
        };
        
        // State
        this.isGrabbed = false;
        this.isDragging = false;
        this.currentRotation = 0;
        this.targetRotation = 0;
        this.rotationSpeed = 0;
        this.lastMouseAngle = 0;
        this.lastUpdateTime = 0;
        this.animationId = null;
        
        // Touch/mouse tracking
        this.startAngle = 0;
        this.lastAngle = 0;
        this.angularVelocity = 0;
        this.lastAngularVelocity = 0;
        
        // Callbacks
        this.onGrab = null;
        this.onRelease = null;
        this.onRotate = null;
        this.onSpinStart = null;
        this.onSpinStop = null;
        
        // Auto-rotation for playback
        this.isAutoRotating = false;
        this.autoRotationSpeed = 0;
        
        this.initializeEvents();
        this.startAnimationLoop();
    }
    
    initializeEvents() {
        // Mouse events
        this.vinylElement.addEventListener('mousedown', this.handleMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // Touch events for mobile support
        this.vinylElement.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
        
        // Prevent context menu on right click
        this.vinylElement.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Prevent text selection
        this.vinylElement.addEventListener('selectstart', (e) => e.preventDefault());
    }
    
    handleMouseDown(event) {
        event.preventDefault();
        this.startGrab(event.clientX, event.clientY);
    }
    
    handleMouseMove(event) {
        if (this.isGrabbed) {
            event.preventDefault();
            this.updateGrab(event.clientX, event.clientY);
        }
    }
    
    handleMouseUp(event) {
        if (this.isGrabbed) {
            this.endGrab();
        }
    }
    
    handleTouchStart(event) {
        event.preventDefault();
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.startGrab(touch.clientX, touch.clientY);
        }
    }
    
    handleTouchMove(event) {
        event.preventDefault();
        if (this.isGrabbed && event.touches.length === 1) {
            const touch = event.touches[0];
            this.updateGrab(touch.clientX, touch.clientY);
        }
    }
    
    handleTouchEnd(event) {
        if (this.isGrabbed) {
            this.endGrab();
        }
    }
    
    startGrab(x, y) {
        this.isGrabbed = true;
        this.isDragging = false;
        this.isAutoRotating = false;
        
        const rect = this.vinylElement.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        this.startAngle = this.getAngle(x, y, centerX, centerY);
        this.lastAngle = this.startAngle;
        this.lastMouseAngle = this.startAngle;
        this.lastUpdateTime = performance.now();
        this.angularVelocity = 0;
        
        // Visual feedback
        this.vinylElement.style.cursor = 'grabbing';
        this.vinylElement.style.transform = 'scale(1.05)';
        
        if (this.onGrab) {
            this.onGrab();
        }
    }
    
    updateGrab(x, y) {
        if (!this.isGrabbed) return;
        
        const rect = this.vinylElement.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const currentAngle = this.getAngle(x, y, centerX, centerY);
        const now = performance.now();
        const timeDelta = (now - this.lastUpdateTime) / 1000;
        
        if (timeDelta > 0) {
            // Calculate angular difference (handle wrap-around)
            let angleDelta = currentAngle - this.lastAngle;
            
            // Normalize angle delta to [-π, π]
            while (angleDelta > Math.PI) angleDelta -= 2 * Math.PI;
            while (angleDelta < -Math.PI) angleDelta += 2 * Math.PI;
            
            // Check if we're actually dragging (moved enough)
            const totalAngleDelta = Math.abs(currentAngle - this.startAngle);
            if (!this.isDragging && totalAngleDelta > 0.1) {
                this.isDragging = true;
                if (this.onSpinStart) {
                    this.onSpinStart();
                }
            }
            
            if (this.isDragging) {
                // Update rotation
                this.currentRotation += angleDelta;
                this.targetRotation = this.currentRotation;
                
                // Calculate angular velocity
                this.angularVelocity = angleDelta / timeDelta;
                
                // Apply smoothing to angular velocity
                this.angularVelocity = this.lastAngularVelocity * 0.7 + this.angularVelocity * 0.3;
                this.lastAngularVelocity = this.angularVelocity;
                
                // Trigger rotation callback
                if (this.onRotate) {
                    this.onRotate(angleDelta, this.angularVelocity, this.options.sensitivity);
                }
            }
        }
        
        this.lastAngle = currentAngle;
        this.lastUpdateTime = now;
    }
    
    endGrab() {
        if (!this.isGrabbed) return;
        
        this.isGrabbed = false;
        
        // Visual feedback
        this.vinylElement.style.cursor = 'grab';
        this.vinylElement.style.transform = 'scale(1)';
        
        // If we were dragging, apply momentum
        if (this.isDragging) {
            this.applyMomentum();
            
            if (this.onSpinStop) {
                this.onSpinStop();
            }
        }
        
        this.isDragging = false;
        
        if (this.onRelease) {
            this.onRelease();
        }
    }
    
    applyMomentum() {
        // Apply momentum based on final angular velocity
        const momentum = this.angularVelocity * 0.5; // Reduce momentum
        this.rotationSpeed = momentum;
        
        // Gradually slow down the momentum
        const decelerate = () => {
            this.rotationSpeed *= 0.95; // Deceleration factor
            this.currentRotation += this.rotationSpeed * 0.016; // Assume 60fps
            
            if (Math.abs(this.rotationSpeed) > 0.01) {
                requestAnimationFrame(decelerate);
            } else {
                this.rotationSpeed = 0;
                // Resume auto-rotation if needed
                if (this.autoRotationSpeed > 0) {
                    this.isAutoRotating = true;
                }
            }
        };
        
        if (Math.abs(this.rotationSpeed) > 0.01) {
            requestAnimationFrame(decelerate);
        }
    }
    
    getAngle(x, y, centerX, centerY) {
        return Math.atan2(y - centerY, x - centerX);
    }
    
    startAnimationLoop() {
        const animate = () => {
            this.updateRotation();
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }
    
    updateRotation() {
        // Auto-rotation for normal playback
        if (this.isAutoRotating && !this.isGrabbed) {
            const rotationDelta = (this.autoRotationSpeed * 2 * Math.PI) / 60; // Convert RPM to rad/frame
            this.currentRotation += rotationDelta / 60; // Assume 60fps
            this.targetRotation = this.currentRotation;
        }
        
        // Apply rotation to vinyl element
        const rotationDegrees = (this.currentRotation * 180) / Math.PI;
        this.vinylElement.style.transform = `rotate(${rotationDegrees}deg)`;
    }
    
    // Public methods
    startAutoRotation(rpm = 33.33) {
        this.autoRotationSpeed = rpm;
        if (!this.isGrabbed) {
            this.isAutoRotating = true;
        }
    }
    
    stopAutoRotation() {
        this.isAutoRotating = false;
        this.autoRotationSpeed = 0;
    }
    
    setRotationSpeed(rpm) {
        this.autoRotationSpeed = rpm;
    }
    
    setSensitivity(sensitivity) {
        this.options.sensitivity = Math.max(0.1, Math.min(3.0, sensitivity));
    }
    
    setTrackLabel(text) {
        if (this.labelElement) {
            this.labelElement.textContent = text;
        }
    }
    
    reset() {
        this.currentRotation = 0;
        this.targetRotation = 0;
        this.rotationSpeed = 0;
        this.angularVelocity = 0;
        this.lastAngularVelocity = 0;
        this.stopAutoRotation();
    }
    
    // Visual effects
    addGlow() {
        this.vinylElement.style.boxShadow = '0 0 30px rgba(78, 205, 196, 0.5), 0 10px 30px rgba(0, 0, 0, 0.5)';
    }
    
    removeGlow() {
        this.vinylElement.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.5)';
    }
    
    addScratchEffect() {
        this.vinylElement.style.filter = 'brightness(1.2) contrast(1.1)';
    }
    
    removeScratchEffect() {
        this.vinylElement.style.filter = 'none';
    }
    
    // Cleanup
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // Remove event listeners
        this.vinylElement.removeEventListener('mousedown', this.handleMouseDown);
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
        this.vinylElement.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchmove', this.handleTouchMove);
        document.removeEventListener('touchend', this.handleTouchEnd);
    }
}

// Export for use in other modules
window.VinylUI = VinylUI;
