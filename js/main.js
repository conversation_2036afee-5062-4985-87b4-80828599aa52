/**
 * Main Application for Virtual Vinyl Turntable
 * Coordinates all components and handles UI interactions
 */

class VinylTurntableApp {
    constructor() {
        // Core components
        this.audioEngine = null;
        this.vinylUI = null;
        this.scratchController = null;
        this.waveformVisualizer = null;
        
        // UI elements
        this.elements = {};
        
        // Application state
        this.currentTrack = null;
        this.settings = {
            sensitivity: 1.0,
            crackleVolume: 0.3,
            volume: 0.8
        };
        
        this.initializeApp();
    }
    
    async initializeApp() {
        try {
            // Get UI elements
            this.getUIElements();
            
            // Initialize core components
            this.audioEngine = new AudioEngine();
            this.vinylUI = new VinylUI(this.elements.vinylRecord);
            this.scratchController = new ScratchController(this.audioEngine, this.vinylUI);
            this.waveformVisualizer = new WaveformVisualizer(this.elements.waveformCanvas);
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Set up audio engine callbacks
            this.setupAudioCallbacks();
            
            // Load settings from localStorage
            this.loadSettings();
            
            // Initialize waveform visualizer
            this.waveformVisualizer.start();
            
            console.log('Virtual Vinyl Turntable initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize the turntable. Please refresh the page.');
        }
    }
    
    getUIElements() {
        this.elements = {
            // Vinyl elements
            vinylRecord: document.getElementById('vinylRecord'),
            trackLabel: document.getElementById('trackLabel'),
            
            // Control elements
            playPauseBtn: document.getElementById('playPauseBtn'),
            playPauseText: document.getElementById('playPauseText'),
            resetBtn: document.getElementById('resetBtn'),
            
            // Status elements
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            currentTime: document.getElementById('currentTime'),
            totalTime: document.getElementById('totalTime'),
            progressBar: document.getElementById('progressBar'),
            
            // Upload elements
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            browseBtn: document.getElementById('browseBtn'),
            
            // Waveform
            waveformCanvas: document.getElementById('waveformCanvas'),
            
            // Settings
            sensitivitySlider: document.getElementById('sensitivitySlider'),
            crackleSlider: document.getElementById('crackleSlider')
        };
    }
    
    setupEventListeners() {
        // File upload
        this.elements.browseBtn.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.loadAudioFile(e.target.files[0]);
            }
        });
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.elements.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.elements.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        
        // Control buttons
        this.elements.playPauseBtn.addEventListener('click', this.togglePlayPause.bind(this));
        this.elements.resetBtn.addEventListener('click', this.resetPlayback.bind(this));
        
        // Settings sliders
        this.elements.sensitivitySlider.addEventListener('input', (e) => {
            this.setSensitivity(parseFloat(e.target.value));
        });
        
        this.elements.crackleSlider.addEventListener('input', (e) => {
            this.setCrackleVolume(parseFloat(e.target.value));
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // Window events
        window.addEventListener('beforeunload', this.saveSettings.bind(this));
    }
    
    setupAudioCallbacks() {
        // Time updates
        this.audioEngine.onTimeUpdate = (currentTime, duration) => {
            this.updateTimeDisplay(currentTime, duration);
            this.updateProgressBar(currentTime, duration);
        };
        
        // Load complete
        this.audioEngine.onLoadComplete = (audioBuffer) => {
            this.onAudioLoaded(audioBuffer);
        };
        
        // Play state changes
        this.audioEngine.onPlayStateChange = (isPlaying) => {
            this.updatePlayState(isPlaying);
        };
    }
    
    async loadAudioFile(file) {
        try {
            this.showStatus('Loading...', 'loading');
            
            // Validate file type
            if (!file.type.includes('audio/mpeg') && !file.name.toLowerCase().endsWith('.mp3')) {
                throw new Error('Please select an MP3 file');
            }
            
            // Load audio
            await this.audioEngine.loadAudioFile(file);
            
            // Update track info
            this.currentTrack = {
                name: file.name.replace(/\.[^/.]+$/, ""), // Remove extension
                file: file,
                duration: this.audioEngine.getDuration()
            };
            
            this.showStatus('Ready', 'ready');
            
        } catch (error) {
            console.error('Failed to load audio file:', error);
            this.showError(error.message || 'Failed to load audio file');
        }
    }
    
    onAudioLoaded(audioBuffer) {
        // Update UI
        this.elements.trackLabel.textContent = this.currentTrack.name;
        this.elements.totalTime.textContent = this.formatTime(audioBuffer.duration);
        
        // Enable controls
        this.elements.playPauseBtn.disabled = false;
        this.elements.resetBtn.disabled = false;
        
        // Update waveform
        this.waveformVisualizer.setAudioBuffer(audioBuffer);
        
        // Set vinyl auto-rotation speed based on track
        this.vinylUI.setRotationSpeed(33.33); // Standard RPM
        
        console.log(`Track loaded: ${this.currentTrack.name} (${this.formatTime(audioBuffer.duration)})`);
    }
    
    togglePlayPause() {
        if (!this.audioEngine.audioBuffer) return;
        
        if (this.audioEngine.isPlaying) {
            this.audioEngine.pause();
        } else {
            this.audioEngine.play();
        }
    }
    
    resetPlayback() {
        this.audioEngine.reset();
        this.vinylUI.reset();
        this.scratchController.reset();
        this.updateTimeDisplay(0, this.audioEngine.getDuration());
        this.updateProgressBar(0, this.audioEngine.getDuration());
    }
    
    updatePlayState(isPlaying) {
        // Update button text
        this.elements.playPauseText.textContent = isPlaying ? 'Pause' : 'Play';
        
        // Update status indicator
        this.elements.statusIndicator.classList.toggle('playing', isPlaying);
        this.elements.statusText.textContent = isPlaying ? 'Playing' : 'Paused';
        
        // Update vinyl rotation
        if (isPlaying) {
            this.vinylUI.startAutoRotation(33.33);
        } else {
            this.vinylUI.stopAutoRotation();
        }
    }
    
    updateTimeDisplay(currentTime, duration) {
        this.elements.currentTime.textContent = this.formatTime(currentTime);
        if (duration > 0) {
            this.elements.totalTime.textContent = this.formatTime(duration);
        }
    }
    
    updateProgressBar(currentTime, duration) {
        if (duration > 0) {
            const progress = (currentTime / duration) * 100;
            this.elements.progressBar.style.width = `${progress}%`;
        }
    }
    
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    showStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        // Update status indicator color
        this.elements.statusIndicator.className = 'status-indicator';
        if (type === 'loading') {
            this.elements.statusIndicator.style.background = '#ffd93d';
        } else if (type === 'ready') {
            this.elements.statusIndicator.style.background = '#4ecdc4';
        } else if (type === 'error') {
            this.elements.statusIndicator.style.background = '#ff6b6b';
        }
    }
    
    showError(message) {
        this.showStatus(message, 'error');
        console.error(message);
    }
    
    // Drag and drop handlers
    handleDragOver(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.add('dragover');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.loadAudioFile(files[0]);
        }
    }
    
    // Keyboard shortcuts
    handleKeyboard(e) {
        if (e.target.tagName === 'INPUT') return; // Don't interfere with input fields
        
        switch (e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'KeyR':
                e.preventDefault();
                this.resetPlayback();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.adjustSensitivity(0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.adjustSensitivity(-0.1);
                break;
        }
    }
    
    // Settings
    setSensitivity(value) {
        this.settings.sensitivity = value;
        this.scratchController.setSensitivity(value);
        this.saveSettings();
    }
    
    setCrackleVolume(value) {
        this.settings.crackleVolume = value;
        this.audioEngine.setCrackleVolume(value);
        this.saveSettings();
    }
    
    adjustSensitivity(delta) {
        const newValue = Math.max(0.1, Math.min(3.0, this.settings.sensitivity + delta));
        this.elements.sensitivitySlider.value = newValue;
        this.setSensitivity(newValue);
    }
    
    loadSettings() {
        try {
            const saved = localStorage.getItem('vinylTurntableSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                this.settings = { ...this.settings, ...settings };
                
                // Apply settings to UI
                this.elements.sensitivitySlider.value = this.settings.sensitivity;
                this.elements.crackleSlider.value = this.settings.crackleVolume;
                
                // Apply to components
                this.setSensitivity(this.settings.sensitivity);
                this.setCrackleVolume(this.settings.crackleVolume);
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
    }
    
    saveSettings() {
        try {
            localStorage.setItem('vinylTurntableSettings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save settings:', error);
        }
    }
}

/**
 * Simple Waveform Visualizer
 */
class WaveformVisualizer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.audioBuffer = null;
        this.animationId = null;
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width * window.devicePixelRatio;
        this.canvas.height = rect.height * window.devicePixelRatio;
        this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    }
    
    setAudioBuffer(audioBuffer) {
        this.audioBuffer = audioBuffer;
        this.drawWaveform();
    }
    
    drawWaveform() {
        if (!this.audioBuffer) {
            this.drawPlaceholder();
            return;
        }
        
        const width = this.canvas.width / window.devicePixelRatio;
        const height = this.canvas.height / window.devicePixelRatio;
        
        this.ctx.clearRect(0, 0, width, height);
        
        // Get audio data
        const channelData = this.audioBuffer.getChannelData(0);
        const samples = Math.min(channelData.length, width * 2);
        const blockSize = Math.floor(channelData.length / samples);
        
        // Draw waveform
        this.ctx.strokeStyle = '#4ecdc4';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        
        for (let i = 0; i < samples; i++) {
            const x = (i / samples) * width;
            let sum = 0;
            
            // Average the block
            for (let j = 0; j < blockSize; j++) {
                sum += Math.abs(channelData[i * blockSize + j]);
            }
            const average = sum / blockSize;
            
            const y = height / 2 + (average * height / 2) * (i % 2 === 0 ? -1 : 1);
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        
        this.ctx.stroke();
    }
    
    drawPlaceholder() {
        const width = this.canvas.width / window.devicePixelRatio;
        const height = this.canvas.height / window.devicePixelRatio;
        
        this.ctx.clearRect(0, 0, width, height);
        
        // Draw placeholder text
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.font = '14px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Load an MP3 to see waveform', width / 2, height / 2);
    }
    
    start() {
        this.drawPlaceholder();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.turntableApp = new VinylTurntableApp();
});
